; PlatformIO Project Configuration File
;
;   Build options: build flags, source filter
;   Upload options: custom upload port, speed and extra flags
;   Library options: dependencies, extra library storages
;   Advanced options: extra scripting
;
; Please visit documentation for the other options and examples
; https://docs.platformio.org/page/projectconf.html

[common]
build_flags = 
	-Isrc
	-Iinclude
	-Ilib/utils/include
	-Ilib/esp32_port/include
	-Ilib/coreHTTP/include
	-Ilib/coreJSON/include
	-Ilib/coreMQTT/include
	-Ilib/middleware/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/porting/nimble/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/porting/npl/freertos/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/gap/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/gatt/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/ans/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/nimble/host/services/gap/include
	-I.pio/libdeps/esp32-c3-devkitm-1/NimBLE-Arduino/src/nimble/esp_port/esp-hci/include
	-DARDUINO_USB_MODE=1
	-DARDUINO_USB_CDC_ON_BOOT=1
	-DTUYA_DEBUG_LOGS=1
lib_deps = 
	utils
	ty_iot
	coreHTTP
	coreJSON
	coreMQTT
	esp32_port
	middleware
	h2zero/NimBLE-Arduino@1.4.3

[env:esp32-c3-devkitm-1]
extends = common
platform = espressif32@6.9.0
board = esp32-c3-devkitm-1
framework = arduino
board_build.mcu = esp32c3
board_build.flash_mode = qio
board_build.flash_size = 4MB
board_build.partitions = partitions_4M.csv
board_build.f_cpu = 80000000L
;upload_protocol = esptool
;upload_port = COM6
;upload_speed = 115200
build_flags = 
	${common.build_flags}
lib_deps = 
	${common.lib_deps}
	mathertel/OneButton@^2.6.1
	fastled/FastLED@^3.10.1
