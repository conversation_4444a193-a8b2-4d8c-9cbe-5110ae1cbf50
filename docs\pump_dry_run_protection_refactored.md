# 智能水泵空转保护功能说明（重构版）

## 功能概述

本功能为家庭小型浇花智能水泵添加了空转检测和保护机制，支持纯水泵模式（MODE_PUMP_FLASH）和臭氧水泵模式（MODE_OZONE_FLASH）的空转检测。

## 重构改进

### 代码重用
- 创建了通用的`handle_pump_dry_run_detection()`函数
- 避免了MODE_PUMP_FLASH和MODE_OZONE_FLASH分支的代码重复
- 提高了代码的可维护性和一致性

### 统一的空转检测逻辑
两种水泵模式都使用相同的检测参数和流程：
- 启动延迟：2秒
- 检测间隔：3秒
- 检测时长：500ms
- 超时时间：30秒

## 工作流程

### 纯水泵模式（按钮6）
1. 按下按钮6 → 启动纯水泵 → 绿色LED亮起
2. 2秒后检测电压 → 如果空转，切换到红色闪烁模式
3. 周期性检测 → 发现有水立即恢复绿色模式

### 臭氧水泵模式（按钮7）
1. 按下按钮7 → 启动臭氧水泵 → 蓝色LED亮起
2. 2秒后检测电压 → 如果空转，切换到红色闪烁模式
3. 周期性检测 → 发现有水立即恢复蓝色模式

### 空转检测模式（MODE_PUMP_DRY_RUN_DETECT）
- 红色LED快速闪烁（200ms间隔）
- 每3秒启动水泵500ms进行检测
- 检测到有水立即返回对应的工作模式
- 30秒超时后返回常规模式

## 核心函数

### `handle_pump_dry_run_detection()`
```cpp
bool handle_pump_dry_run_detection(uint32_t &start_time, bool &check_done, LedMode current_mode)
```
- **参数**：
  - `start_time`: 水泵启动时间（引用传递，保持状态）
  - `check_done`: 是否已完成检测（引用传递，保持状态）
  - `current_mode`: 当前LED模式（用于状态重置）
- **返回值**：
  - `true`: 检测到空转，需要切换到检测模式
  - `false`: 正常工作或等待中

### `check_pump_dry_run()`
```cpp
bool check_pump_dry_run()
```
- 读取水泵电压并判断是否在空转范围内
- 空转判断：电压 ≤ 1.2V

## LED状态指示

| LED状态 | 含义 | 模式 |
|---------|------|------|
| 🟢 绿色常亮 | 纯水泵正常工作 | MODE_PUMP_FLASH |
| 🔵 蓝色常亮 | 臭氧水泵正常工作 | MODE_OZONE_FLASH |
| 🔴 红色快速闪烁 | 空转检测中 | MODE_PUMP_DRY_RUN_DETECT |

## 配置参数

```cpp
#define DRY_RUN_VOLTAGE_MAX 1.2       // 空转电压上限 (V)
#define PUMP_DETECTION_TIME 500       // 水泵检测运行时间 (ms)
#define WATER_DETECTION_INTERVAL 3000 // 检测间隔 (ms)
#define MAX_DETECTION_DURATION 30000  // 最大检测时长 (ms)
#define PUMP_STARTUP_DELAY 2000       // 水泵启动延迟 (ms)
```

## 优势特性

1. **代码重用**：两种水泵模式共享相同的检测逻辑
2. **状态独立**：每种模式维护独立的计时器和状态
3. **安全保护**：立即停止空转，最小化设备损坏风险
4. **用户友好**：清晰的LED状态指示
5. **可配置**：所有参数都可以根据实际需求调整

## 使用建议

1. 根据实际水泵特性调整`DRY_RUN_VOLTAGE_MAX`参数
2. 可以根据水管长度调整`PUMP_STARTUP_DELAY`
3. 建议在实际环境中测试并优化检测参数
4. 定期检查水源，避免频繁触发空转保护
