#include "device_control.h"
#include <OneButton.h>
#include <FastLED.h>
#include "tuya_log.h"
#include "shipping.h"
#include "esp32_adc.h"

// 引脚定义
#define LED_PIN 10
#define BTN_PIN_6 6
#define BTN_PIN_7 7
#define GPIO_PIN_5 5
#define GPIO_PIN_1 1
#define GPIO_PIN_0 0

adc_config_t adc_config_pump = {
    .voltage_divider_factor = 1.00,
    .adc_pin = 3,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_3,
    .adc_atten = ADC_ATTEN_DB_12,
    .adc_width = ADC_WIDTH_BIT_12,
    .default_vref = 1100,
    .adc1_chan_mask = BIT(3),
    .adc2_chan_mask = 0x0,
    .channel = {(adc_channel_t)ADC1_CHANNEL_3},
};

adc_config_t adc_config_vbat = {
    .voltage_divider_factor = 2.00,
    .adc_pin = 4,
    .adc_unit = ADC_UNIT_1,
    .adc_channel = ADC1_CHANNEL_4,
    .adc_atten = ADC_ATTEN_DB_12,
    .adc_width = ADC_WIDTH_BIT_12,
    .default_vref = 1100,
    .adc1_chan_mask = BIT(4),
    .adc2_chan_mask = 0x0,
    .channel = {(adc_channel_t)ADC1_CHANNEL_4},
};

// LED 配置
#define NUM_LEDS 1
CRGB leds[NUM_LEDS];

// OneButton 实例 (低电平有效, 启用内部上拉)
// Declared static to avoid conflicts with other OneButton instances in the project (e.g., in shipping.cpp)
static OneButton button6(BTN_PIN_6, true, true);
static OneButton button7(BTN_PIN_7, true, true);

// 状态变量
enum LedMode {
    MODE_WIFI_CONNECT_STATUS,
    MODE_TUYAIOT_STATUS,
    MODE_PUMP_FLASH,
    MODE_OZONE_FLASH,
    MODE_FACTORY_RESET_BLINK,
    MODE_PUMP_DRY_RUN_DETECT  // 新增：水泵空转检测模式
};
volatile LedMode current_led_mode = MODE_TUYAIOT_STATUS;
volatile bool tuyaiot_connected = false;
volatile bool wifi_connected = false;
bool gpios_on = false; // 新增一个独立的GPIO状态变量

// 水泵空转检测相关变量
#define DRY_RUN_VOLTAGE_MAX 1.8   // 空转电压上限 (V)
#define PUMP_DETECTION_TIME 500   // 水泵检测运行时间 (ms)
#define WATER_DETECTION_INTERVAL 3000  // 检测间隔 (ms)
#define MAX_DETECTION_DURATION 30000   // 最大检测时长 (ms)
#define PUMP_STARTUP_DELAY 2000   // 水泵启动延迟，等待水管吸水 (ms)

volatile bool pump_dry_run_detected = false;
volatile bool is_detecting_water = false;

// 函数声明
void handle_button6_click();
void handle_button7_click();
void handle_long_press_start();
void handle_during_long_press();
void handle_long_press_stop();
void led_task(void *pvParameters);
bool check_pump_dry_run();
bool handle_pump_dry_run_detection(uint32_t &start_time, bool &check_done, LedMode current_mode);

void device_control_init()
{
    // 初始化 ADC采样电机和电池
    adc_single_init(&adc_config_pump);
    adc_calibration(&adc_config_pump);
    adc_single_init(&adc_config_vbat);
    adc_calibration(&adc_config_vbat);
    // 初始化 GPIO
    pinMode(GPIO_PIN_5, OUTPUT);
    pinMode(GPIO_PIN_1, OUTPUT);
    pinMode(GPIO_PIN_0, OUTPUT);
    digitalWrite(GPIO_PIN_5, LOW);
    digitalWrite(GPIO_PIN_1, LOW);
    digitalWrite(GPIO_PIN_0, LOW);  //关断水泵和臭氧总电源

    // 初始化 FastLED
    FastLED.addLeds<WS2812B, LED_PIN, GRB>(leds, NUM_LEDS);
    FastLED.setBrightness(255);

    // 绑定按键事件
    button6.attachClick(handle_button6_click);
    button6.attachLongPressStart(handle_long_press_start);
    button6.attachDuringLongPress(handle_during_long_press);
    button6.attachLongPressStop(handle_long_press_stop);
    button6.setPressMs(2000); // 长按2000ms后开始检测
    button6.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发

    button7.attachClick(handle_button7_click);
    button7.attachLongPressStart(handle_long_press_start);
    button7.attachDuringLongPress(handle_during_long_press);
    button7.attachLongPressStop(handle_long_press_stop);
    button7.setPressMs(2000); // 长按2000ms后开始检测
    button7.setDebounceMs(50); // 增加去抖动以防止物理按键抖动导致误触发

    // 创建 LED 控制任务，增加堆栈大小以策安全
    xTaskCreate(led_task, "led_task", 2048, NULL, 5, NULL);
}

void device_control_loop()
{
    button6.tick();
    button7.tick();
}

void device_control_tuya_iot_status(bool connected)
{
    tuyaiot_connected = connected;
    current_led_mode = MODE_TUYAIOT_STATUS;
}

void device_control_wifi_connected_flash(bool connected)
{
    current_led_mode = MODE_WIFI_CONNECT_STATUS;
    wifi_connected = connected;
}

void handle_button6_click() // PUMP
{
    TY_LOGI("Button 6 clicked");
    pump_dry_run_detected = false;
    is_detecting_water = false;
    // Case 1: GPIOs are on and we are showing GREEN. Turn everything off.
    if (gpios_on && (current_led_mode == MODE_PUMP_FLASH || current_led_mode == MODE_PUMP_DRY_RUN_DETECT)) {
        gpios_on = false;
        current_led_mode = MODE_TUYAIOT_STATUS;
        digitalWrite(GPIO_PIN_5, LOW);
        digitalWrite(GPIO_PIN_1, LOW);
        digitalWrite(GPIO_PIN_0, LOW);  //水泵和臭氧总电源
    }
    // Case 2: GPIOs are off, OR we are showing GREEN. Turn on GPIOs and switch to PUMP mode.
    else {
        gpios_on = true;
        current_led_mode = MODE_PUMP_FLASH;
        digitalWrite(GPIO_PIN_0, HIGH);  //水泵和臭氧总电源
        digitalWrite(GPIO_PIN_5, HIGH);
        digitalWrite(GPIO_PIN_1, LOW);
    }
}

void handle_button7_click() // OZONE
{
    TY_LOGI("Button 7 clicked");
    pump_dry_run_detected = false;
    is_detecting_water = false;
    // Case 1: GPIOs are on and we are showing GREEN. Turn everything off.
    if (gpios_on && (current_led_mode == MODE_OZONE_FLASH || current_led_mode == MODE_PUMP_DRY_RUN_DETECT)) {
        gpios_on = false;
        current_led_mode = MODE_TUYAIOT_STATUS;
        digitalWrite(GPIO_PIN_5, LOW);
        digitalWrite(GPIO_PIN_1, LOW);
        digitalWrite(GPIO_PIN_0, LOW);  //水泵和臭氧总电源
    }
    // Case 2: GPIOs are off, OR we are showing BLUE. Turn on GPIOs and switch to OZONE mode.
    else {
        gpios_on = true;
        current_led_mode = MODE_OZONE_FLASH;
        digitalWrite(GPIO_PIN_0, HIGH);  //水泵和臭氧总电源
        digitalWrite(GPIO_PIN_5, HIGH);
        digitalWrite(GPIO_PIN_1, HIGH);
    }
}

void handle_long_press_start()
{
    TY_LOGI("Long press start detected. Starting factory reset blink.");
    current_led_mode = MODE_FACTORY_RESET_BLINK;
}

void handle_during_long_press()
{
    static bool reset_triggered = false;
    if (reset_triggered) return;

    // 检查任一按钮的长按时间是否超过10秒
    if (button6.getPressedMs() >= 10000 || button7.getPressedMs() >= 10000) {
        reset_triggered = true;
        TY_LOGI("Factory reset triggered by holding for 10s!");
        factory_reset();
        ESP.restart();
    }
}

void handle_long_press_stop()
{
    TY_LOGI("Long press stopped.");
    // 如果长按被释放（无论是否触发了重置），都恢复正常状态
    // 如果已经触发重置，设备会重启，这里只是一个安全回退
    current_led_mode = MODE_TUYAIOT_STATUS;
}

void led_task(void *pvParameters)
{
    bool led_on = false;
    int factory_blink_delay = 500;
    int count = 0;
    UBaseType_t stackLeft;
    uint8_t breathing_phase = 0; // 呼吸灯相位，0-255
    float vref = 0.0;

    while (true) {
        // 如果不是在恢复出厂设置模式，则重置闪烁延迟
        if (current_led_mode != MODE_FACTORY_RESET_BLINK) {
            factory_blink_delay = 500;
        }

        switch (current_led_mode) {
            case MODE_TUYAIOT_STATUS:
                if (tuyaiot_connected) {
                    // TuyaIoT 已连接：呼吸灯模式 (DarkViolet)
                    breathing_phase += 1; // 减慢呼吸速度，让效果更明显

                    // 使用正弦波计算亮度 (0-255)
                    // sin8() 返回 0-255，我们将其映射到更大的亮度范围以增强效果
                    uint8_t brightness = sin8(breathing_phase);
                    brightness = map(brightness, 0, 255, 0, 120);

                    leds[0] = CRGB::DarkViolet;
                    leds[0].nscale8(brightness);

                    FastLED.show();
                    if ( brightness != 0 )
                        vTaskDelay(pdMS_TO_TICKS(10)); // 减少延迟以保持平滑度
                    else
                        vTaskDelay(pdMS_TO_TICKS(250));
                } else {
                    // TuyaIoT 未连接：闪烁模式 (Red)
                    led_on = !led_on;
                    if (led_on) {
                        leds[0] = CRGB::Red;
                        leds[0].nscale8(80);
                    } else {
                        leds[0] = CRGB::Black;
                    }
                    FastLED.show();
                    vTaskDelay(pdMS_TO_TICKS(500)); // 闪烁间隔
                }
                break;

            case MODE_PUMP_FLASH:
                {
                    static uint32_t pump_start_time = 0;
                    static bool dry_run_check_done = false;

                    // 使用通用空转检测函数
                    if (handle_pump_dry_run_detection(pump_start_time, dry_run_check_done, MODE_PUMP_FLASH)) {
                        break;  // 检测到空转，已切换到检测模式
                    }

                    // 显示绿色LED表示水泵正常工作
                    leds[0] = CRGB::Green;
                    leds[0].nscale8(80);
                    FastLED.show();
                    vTaskDelay(pdMS_TO_TICKS(250));
                }
                break;

            case MODE_OZONE_FLASH:
                {
                    static uint32_t ozone_start_time = 0;
                    static bool ozone_dry_run_check_done = false;

                    // 使用通用空转检测函数
                    if (handle_pump_dry_run_detection(ozone_start_time, ozone_dry_run_check_done, MODE_OZONE_FLASH)) {
                        break;  // 检测到空转，已切换到检测模式
                    }

                    // 显示蓝色LED表示臭氧水泵正常工作
                    leds[0] = CRGB::Blue;
                    leds[0].nscale8(200);
                    FastLED.show();
                    vTaskDelay(pdMS_TO_TICKS(250));
                }
                break;
            
            case MODE_FACTORY_RESET_BLINK:
                leds[0] = CRGB::Red;
                leds[0].nscale8(80);
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));
                leds[0] = CRGB::Black;
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(factory_blink_delay));

                if (factory_blink_delay > 50) {
                    factory_blink_delay -= 45; // 加速闪烁
                }
                break;

            case MODE_PUMP_DRY_RUN_DETECT:
                {
                    static uint32_t detection_start_time = 0;
                    static uint32_t last_detection_time = 0;
                    static bool pump_test_running = false;

                    uint32_t current_time = millis();

                    // 初始化检测开始时间
                    if (detection_start_time == 0) {
                        detection_start_time = current_time;
                        last_detection_time = current_time;
                        TY_LOGI("Starting water detection cycle");
                    }

                    // 检查是否超过最大检测时长
                    if (current_time - detection_start_time >= MAX_DETECTION_DURATION) {
                        TY_LOGW("Water detection timeout, returning to normal mode");
                        pump_dry_run_detected = false;
                        is_detecting_water = false;
                        current_led_mode = MODE_TUYAIOT_STATUS;
                        detection_start_time = 0;
                        digitalWrite(GPIO_PIN_5, LOW);  // 确保水泵关闭
                        break;
                    }

                    // 检测间隔控制
                    if (!pump_test_running && (current_time - last_detection_time >= WATER_DETECTION_INTERVAL)) {
                        TY_LOGI("Testing pump for water availability...");
                        digitalWrite(GPIO_PIN_5, HIGH);  // 启动水泵
                        pump_test_running = true;
                        last_detection_time = current_time;
                    }

                    // 水泵运行检测时间后检查电压
                    if (pump_test_running && (current_time - last_detection_time >= PUMP_DETECTION_TIME)) {
                        if (!check_pump_dry_run()) {
                            // 检测到有水，切换到正常水泵模式
                            TY_LOGI("Water detected! Switching to normal pump mode");
                            pump_dry_run_detected = false;
                            is_detecting_water = false;
                            current_led_mode = MODE_PUMP_FLASH;
                            detection_start_time = 0;
                            // 保持水泵运行
                        } else {
                            // 仍然空转，关闭水泵等待下次检测
                            TY_LOGD("Still dry run, stopping pump");
                            digitalWrite(GPIO_PIN_5, LOW);
                        }
                        pump_test_running = false;
                    }

                    // LED闪烁红色表示空转检测状态
                    led_on = !led_on;
                    if (led_on) {
                        leds[0] = CRGB::Red;
                        leds[0].nscale8(120);
                    } else {
                        leds[0] = CRGB::Black;
                    }
                    FastLED.show();
                    vTaskDelay(pdMS_TO_TICKS(200)); // 快速闪烁
                }
                break;

            case MODE_WIFI_CONNECT_STATUS:
                led_on = !led_on;
                if (led_on) {
                    leds[0] = wifi_connected ? CRGB::Yellow : CRGB::Red;
                    leds[0].nscale8(80);
                } else {
                    leds[0] = CRGB::Black;
                }
                FastLED.show();
                vTaskDelay(pdMS_TO_TICKS(250));
                break;
        }
#if 0
        // 每约1-2秒打印一次采样结果
        if (++count >= 5) {
            vref = ReadVoltageSingle(&adc_config_pump);
            TY_LOGI("[led_task] Pump Vref: %f", vref);
            count = 0;
        }
#endif
#if 1
        // 每约5秒打印一次堆栈剩余空间
        if (++count >= 10) {
            stackLeft = uxTaskGetStackHighWaterMark(NULL);
            TY_LOGD("[led_task] Stack high water mark: %d", stackLeft);
            count = 0;
        }
#endif
    }
}

// 检测水泵是否空转
bool check_pump_dry_run()
{
    float voltage = ReadVoltageSingle(&adc_config_pump);
    TY_LOGI("Pump voltage: %.3f V", voltage);

    // 判断电压是否在空转范围内
    return (voltage <= DRY_RUN_VOLTAGE_MAX);
}

// 通用的水泵空转检测处理函数
bool handle_pump_dry_run_detection(uint32_t &start_time, bool &check_done, LedMode current_mode)
{
    uint32_t current_time = millis();

    // 记录水泵启动时间
    if (start_time == 0) {
        start_time = current_time;
        check_done = false;
        TY_LOGI("Pump started, waiting for startup delay");
    }

    // 启动延迟后进行空转检测
    if (!check_done && (current_time - start_time >= PUMP_STARTUP_DELAY)) {
        if (check_pump_dry_run()) {
            TY_LOGW("Pump dry run detected! Starting water detection cycle...");
            pump_dry_run_detected = true;
            is_detecting_water = true;
            current_led_mode = MODE_PUMP_DRY_RUN_DETECT;
            start_time = 0;  // 重置计时器
            digitalWrite(GPIO_PIN_5, LOW);  // 立即关闭水泵
            return true;  // 表示检测到空转，需要切换模式
        } else {
            TY_LOGI("Pump working normally with water flow");
            check_done = true;
        }
    }

    // 如果切换到其他模式，重置计时器
    if (current_led_mode != current_mode) {
        start_time = 0;
        check_done = false;
    }

    return false;  // 表示正常工作，不需要切换模式
}

